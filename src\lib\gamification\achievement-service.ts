import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { notificationQueue } from "@/lib/queue";

export class AchievementService {
  static async createAchievement(data: {
    name: string;
    description: string;
    icon?: string;
    category: string;
    criteria: any;
    points?: number;
    rarity?: string;
  }) {
    return await prisma.achievement.create({
      data: {
        name: data.name,
        description: data.description,
        icon: data.icon,
        category: data.category,
        criteria: JSON.stringify(data.criteria),
        points: data.points || 10,
        rarity: data.rarity || "common",
      },
    });
  }

  static async checkAndAwardAchievements(
    userId: string,
    eventType: string,
    eventData: any
  ) {
    try {
      // Get all achievements that match the event type
      const achievements = await prisma.achievement.findMany({
        where: {
          criteria: {
            contains: eventType,
          },
        },
      });

      const awardedAchievements = [];

      for (const achievement of achievements) {
        const criteria = JSON.parse(achievement.criteria);

        // Check if user meets criteria
        const meetsCriteria = await this.checkCriteria(
          userId,
          criteria,
          eventData
        );

        if (meetsCriteria) {
          const awarded = await this.awardAchievement(userId, achievement.id);
          if (awarded) {
            awardedAchievements.push(achievement);
          }
        }
      }

      // Send notifications for awarded achievements
      for (const achievement of awardedAchievements) {
        await notificationQueue.add("notification", {
          userId,
          title: "Achievement Unlocked!",
          message: `You've earned the "${achievement.name}" achievement`,
          type: "achievement",
          meta: {
            achievementId: achievement.id,
          },
        });
      }

      return awardedAchievements;
    } catch (error) {
      logger.error("Failed to check and award achievements:", error);
      return [];
    }
  }

  private static async checkCriteria(
    userId: string,
    criteria: any,
    eventData: any
  ): Promise<boolean> {
    // Check different types of criteria
    switch (criteria.type) {
      case "video_upload_count":
        const uploadCount = await prisma.video.count({
          where: { creatorId: userId },
        });
        return uploadCount >= criteria.count;

      case "video_view_count":
        const viewCount = await prisma.view.count({
          where: { userId },
        });
        return viewCount >= criteria.count;

      case "follower_count":
        const followerCount = await prisma.follow.count({
          where: { followingId: userId },
        });
        return followerCount >= criteria.count;

      case "comment_count":
        const commentCount = await prisma.comment.count({
          where: { userId },
        });
        return commentCount >= criteria.count;

      case "like_count":
        const likeCount = await prisma.like.count({
          where: {
            AND: [{ userId }, { type: "like" }],
          },
        });
        return likeCount >= criteria.count;

      case "subscription_count":
        const subscriptionCount = await prisma.subscription.count({
          where: { userId },
        });
        return subscriptionCount >= criteria.count;

      case "specific_video":
        return eventData.videoId === criteria.videoId;

      case "time_based":
        const now = new Date();
        return (
          now >= new Date(criteria.startDate) &&
          now <= new Date(criteria.endDate)
        );

      default:
        return false;
    }
  }

  static async awardAchievement(userId: string, achievementId: string) {
    try {
      // Check if user already has this achievement
      const existing = await prisma.userAchievement.findUnique({
        where: {
          userId_achievementId: {
            userId,
            achievementId,
          },
        },
      });

      if (existing && existing.earnedAt) {
        return false; // Already earned
      }

      // Get achievement details
      const achievement = await prisma.achievement.findUnique({
        where: { id: achievementId },
      });

      if (!achievement) {
        throw new Error("Achievement not found");
      }

      // Award achievement
      await prisma.userAchievement.update({
        where: {
          userId_achievementId: {
            userId,
            achievementId,
          },
        },
        data: {
          earnedAt: new Date(),
          progress: 100,
        },
      });

      // Award experience points
      await this.awardExperience(userId, achievement.points);

      // Update leaderboard
      await this.updateLeaderboard(userId, achievement.points);

      logger.info("Achievement awarded", { userId, achievementId });
      return true;
    } catch (error) {
      logger.error("Failed to award achievement:", error);
      return false;
    }
  }

  static async awardExperience(userId: string, points: number) {
    try {
      // Update user level
      const userLevel = await prisma.userLevel.upsert({
        where: { userId },
        update: {
          experience: { increment: points },
        },
        create: {
          userId,
          experience: points,
        },
      });

      // Check for level up
      const newLevel = this.calculateLevel(userLevel.experience);
      if (newLevel > userLevel.level) {
        await prisma.userLevel.update({
          where: { userId },
          data: {
            level: newLevel,
          },
        });

        // Send level up notification
        await notificationQueue.add("notification", {
          userId,
          title: "Level Up!",
          message: `You've reached level ${newLevel}`,
          type: "level_up",
        });
      }
    } catch (error) {
      logger.error("Failed to award experience:", error);
    }
  }

  private static calculateLevel(experience: number): number {
    // Simple level calculation formula
    return Math.floor(Math.sqrt(experience / 100)) + 1;
  }

  static async updateLeaderboard(userId: string, points: number) {
    try {
      const periods = ["daily", "weekly", "monthly", "all_time"];

      for (const period of periods) {
        await prisma.leaderboard.upsert({
          where: {
            userId_period: {
              userId,
              period,
            },
          },
          update: {
            points: { increment: points },
          },
          create: {
            userId,
            points,
            period,
          },
        });
      }

      // Update rankings
      await this.updateRankings();
    } catch (error) {
      logger.error("Failed to update leaderboard:", error);
    }
  }

  private static async updateRankings() {
    const periods = ["daily", "weekly", "monthly", "all_time"];

    for (const period of periods) {
      const leaderboardEntries = await prisma.leaderboard.findMany({
        where: { period },
        orderBy: { points: "desc" },
      });

      // Update ranks
      for (let i = 0; i < leaderboardEntries.length; i++) {
        await prisma.leaderboard.update({
          where: { id: leaderboardEntries[i].id },
          data: {
            rank: i + 1,
          },
        });
      }
    }
  }

  static async getUserAchievements(userId: string) {
    return await prisma.userAchievement.findMany({
      where: { userId },
      include: {
        achievement: true,
      },
      orderBy: { earnedAt: "desc" },
    });
  }

  static async getUserLevel(userId: string) {
    const userLevel = await prisma.userLevel.findUnique({
      where: { userId },
    });

    if (!userLevel) {
      return {
        level: 1,
        experience: 0,
        nextLevelExp: 100,
        progress: 0,
      };
    }

    const nextLevelExp = this.calculateNextLevelExp(userLevel.level);
    const progress = (userLevel.experience / nextLevelExp) * 100;

    return {
      level: userLevel.level,
      experience: userLevel.experience,
      nextLevelExp,
      progress: Math.min(100, Math.max(0, progress)),
    };
  }

  private static calculateNextLevelExp(level: number): number {
    return Math.pow(level, 2) * 100;
  }

  static async getLeaderboard(period: string = "all_time", limit: number = 50) {
    return await prisma.leaderboard.findMany({
      where: { period },
      include: {
        user: {
          select: {
            name: true,
            image: true,
          },
        },
      },
      orderBy: { points: "desc" },
      take: limit,
    });
  }

  static async getRarestAchievements(limit: number = 10) {
    // Get achievements with lowest earn rates
    const achievements = await prisma.achievement.findMany({
      include: {
        _count: {
          select: {
            userAchievements: {
              where: {
                earnedAt: { not: null },
              },
            },
          },
        },
      },
    });

    return achievements
      .sort(
        (a: any, b: any) =>
          a._count.userAchievements - b._count.userAchievements
      )
      .slice(0, limit);
  }
}
