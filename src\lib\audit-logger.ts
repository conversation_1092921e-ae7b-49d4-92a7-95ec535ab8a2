import { prisma } from '@/lib/prisma'
import { logger } from '@/lib/logger'

export class AuditLogger {
  static async logAction(
    userId: string | null,
    action: string,
    resource: string,
    details: any = {}
  ) {
    try {
      await prisma.auditLog.create({
         {
          userId,
          action,
          resource,
          details: JSON.stringify(details),
          ipAddress: details.ip || 'unknown',
          userAgent: details.userAgent || 'unknown'
        }
      })
      
      logger.info('Audit log created', {
        userId,
        action,
        resource,
        details
      })
    } catch (error) {
      logger.error('Failed to create audit log:', error)
    }
  }

  static async getAuditLogs(
    userId?: string,
    action?: string,
    limit: number = 50
  ) {
    try {
      const where: any = {}
      if (userId) where.userId = userId
      if (action) where.action = action

      return await prisma.auditLog.findMany({
        where,
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      })
    } catch (error) {
      logger.error('Failed to fetch audit logs:', error)
      return []
    }
  }
}