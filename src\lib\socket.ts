// lib/socket.ts
import { logger } from "@/lib/logger";
import { Server as HTTPServer } from "http";
import { Server as SocketIOServer } from "socket.io";

export class SocketService {
  private static io: SocketIOServer | null = null;

  static initialize(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
      },
    });

    this.setupEventHandlers();
  }

  private static setupEventHandlers() {
    if (!this.io) return;

    this.io.on("connection", (socket) => {
      logger.info("User connected:", socket.id);

      // Join room for specific video
      socket.on("join-video-room", (videoId) => {
        socket.join(`video-${videoId}`);
        logger.info(`User ${socket.id} joined video room ${videoId}`);
      });

      // Leave video room
      socket.on("leave-video-room", (videoId) => {
        socket.leave(`video-${videoId}`);
        logger.info(`User ${socket.id} left video room ${videoId}`);
      });

      // New comment
      socket.on("new-comment", async (data) => {
        // Broadcast to all users in the video room
        this.io?.to(`video-${data.videoId}`).emit("comment-added", data);

        // Send notification to video creator
        this.io?.to(`user-${data.creatorId}`).emit("new-notification", {
          title: "New Comment",
          message: `${data.userName} commented on your video`,
        });
      });

      // Live streaming
      socket.on("live-stream", (data) => {
        // Broadcast live stream data to viewers
        this.io?.to(`live-${data.channelId}`).emit("stream-data", data);
      });

      // User disconnect
      socket.on("disconnect", () => {
        logger.info("User disconnected:", socket.id);
      });
    });
  }

  static emitToVideoRoom(videoId: string, event: string, data: any) {
    this.io?.to(`video-${videoId}`).emit(event, data);
  }

  static emitToUser(userId: string, event: string, any) {
    this.io?.to(`user-${userId}`).emit(event, data);
  }
}
