"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useUserStore } from "@/lib/store/useUserStore";
import { Coins, Heart } from "lucide-react";
import { useState } from "react";

interface TipJarProps {
  creatorId: string;
  creatorName: string;
}

export function TipJar({ creatorId, creatorName }: TipJarProps) {
  const { user, balance } = useUserStore();
  const [amount, setAmount] = useState("5000");
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");

  const presetAmounts = [5000, 10000, 25000, 50000, 100000];

  const handleTip = async () => {
    if (!user) {
      setError("Please sign in to send tips");
      return;
    }

    const tipAmount = parseInt(amount);
    if (tipAmount < 1000) {
      setError("Minimum tip is Rp1,000");
      return;
    }

    if (tipAmount > balance) {
      setError("Insufficient balance");
      return;
    }

    setLoading(true);
    setError("");
    setSuccess(false);

    try {
      const response = await fetch("/api/tips", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          toUserId: creatorId,
          amount: tipAmount,
          message,
        }),
      });

      if (response.ok) {
        setSuccess(true);
        setAmount("5000");
        setMessage("");
        // Update user balance in store
        useUserStore.getState().deductBalance(tipAmount);
      } else {
        const data = await response.json();
        throw new Error(data.error || "Failed to send tip");
      }
    } catch (err: any) {
      setError(err.message || "Failed to send tip");
    } finally {
      setLoading(false);
    }
  };

  if (user?.id === creatorId) {
    return null; // Don't show tip jar to creator themselves
  }

  return (
    <Card className="border-2 border-pink-100">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Heart className="h-5 w-5 text-pink-500" />
          Support {creatorName}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Amount (Rp)</Label>
            <div className="grid grid-cols-3 gap-2">
              {presetAmounts.map((preset) => (
                <Button
                  key={preset}
                  variant={amount === preset.toString() ? "default" : "outline"}
                  size="sm"
                  onClick={() => setAmount(preset.toString())}
                >
                  {preset.toLocaleString()}
                </Button>
              ))}
            </div>
            <Input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              min="1000"
              step="1000"
              placeholder="Custom amount"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="message">Message (optional)</Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Say something nice..."
              rows={3}
            />
          </div>

          {error && <p className="text-sm text-red-500">{error}</p>}

          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <p>Thank you for your support! 🎉</p>
            </div>
          )}

          <Button
            onClick={handleTip}
            disabled={loading || !user}
            className="w-full bg-pink-500 hover:bg-pink-600"
          >
            {loading ? (
              <span className="flex items-center gap-2">
                <Coins className="h-4 w-4 animate-spin" />
                Sending...
              </span>
            ) : (
              <span className="flex items-center gap-2">
                <Heart className="h-4 w-4" />
                Send Tip
              </span>
            )}
          </Button>

          {!user && (
            <p className="text-sm text-muted-foreground text-center">
              Sign in to support creators
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
