// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DATABASE_URL")
}

model User {
  id                   String                 @id @default(cuid())
  name                 String?
  email                String?                @unique
  emailVerified        DateTime?
  image                String?
  role                 String                 @default("USER")
  balance              Int                    @default(0)
  purchases            Purchase[]
  videos               Video[]                @relation("CreatorVideos")
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  Account              Account[]
  Session              Session[]
  Like                 Like[]
  Comment              Comment[]
  Notification         Notification[]
  subscriptions        Subscription[]         @relation("subscribers")
  subscribedTo         Subscription[]         @relation("subscriptions")
  userLevels           UserLevel[]
  leaderboards         Leaderboard[]
  userAchievements     UserAchievement[]
  Achievement          Achievement[]
  ChallengeParticipant ChallengeParticipant[]
  PPVEvent             PPVEvent[]             @relation("PPVEvents")
  PPVPurchase          PPVPurchase[]
  ReceivedTip          Tip[]                  @relation("ReceivedTips")
  SentTip              Tip[]                  @relation("SentTips")
  Affiliate            Affiliate[]
  AffiliateProgram     AffiliateProgram[]     @relation("AffiliatePrograms")
  AffiliateCommission  AffiliateCommission[]  @relation("AffiliateCommissions")
  SupportTicket        SupportTicket[]
  SupportResponse      SupportResponse[]      @relation("SupportResponses")

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Video {
  id          String     @id @default(cuid())
  title       String
  description String?
  url         String
  thumbnail   String?
  price       Int
  creatorId   String
  creator     User       @relation(name: "CreatorVideos", fields: [creatorId], references: [id])
  purchases   Purchase[]
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  Like        Like[]
  Comment     Comment[]

  @@map("videos")
}

model Subscription {
  id        String   @id @default(cuid())
  userId    String
  creatorId String
  startDate DateTime @default(now())
  endDate   DateTime
  isActive  Boolean  @default(true)
  user      User     @relation(name: "subscriptions", fields: [userId], references: [id], map: "subscriptions_user_fkey")
  creator   User     @relation(name: "subscribers", fields: [creatorId], references: [id], map: "subscriptions_creator_fkey")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("subscriptions")
}

model Purchase {
  id        String   @id @default(cuid())
  userId    String
  videoId   String
  amount    Int
  user      User     @relation(fields: [userId], references: [id])
  video     Video    @relation(fields: [videoId], references: [id])
  createdAt DateTime @default(now())

  @@map("purchases")
}

model Like {
  id        String   @id @default(cuid())
  userId    String
  videoId   String
  type      String   @default("like") // "like" or "dislike"
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  video     Video    @relation(fields: [videoId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@unique([userId, videoId])
  @@map("likes")
}

model Comment {
  id        String    @id @default(cuid())
  content   String
  userId    String
  videoId   String
  parentId  String?
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  video     Video     @relation(fields: [videoId], references: [id], onDelete: Cascade)
  parent    Comment?  @relation(name: "CommentReplies", fields: [parentId], references: [id])
  replies   Comment[] @relation(name: "CommentReplies")
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@map("comments")
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  title     String
  message   String
  type      String   @default("info")
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model Achievement {
  id          String   @id @default(cuid())
  name        String
  description String
  icon        String?
  category    String // upload, social, engagement, milestone
  criteria    String // JSON criteria for earning
  points      Int      @default(10)
  rarity      String   @default("common") // common, rare, epic, legendary
  createdAt   DateTime @default(now())

  userAchievements UserAchievement[]
  User             User[]

  @@map("achievements")
}

model UserAchievement {
  id            String      @id @default(cuid())
  userId        String
  achievementId String
  progress      Int         @default(0)
  earnedAt      DateTime?
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  achievement   Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId])
  @@map("user_achievements")
}

model UserLevel {
  id         String   @id @default(cuid())
  userId     String
  level      Int      @default(1)
  experience Int      @default(0)
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([userId])
  @@map("user_levels")
}

model Leaderboard {
  id        String   @id @default(cuid())
  userId    String
  points    Int      @default(0)
  rank      Int?
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  period    String // daily, weekly, monthly, all_time
  updatedAt DateTime @updatedAt

  @@unique([userId, period])
  @@map("leaderboards")
}

model Challenge {
  id           String                 @id @default(cuid())
  name         String
  description  String
  startDate    DateTime
  endDate      DateTime
  rewards      String // JSON rewards
  participants ChallengeParticipant[]
  createdAt    DateTime               @default(now())

  @@map("challenges")
}

model ChallengeParticipant {
  id          String    @id @default(cuid())
  challengeId String
  userId      String
  progress    Int       @default(0)
  completedAt DateTime?
  challenge   Challenge @relation(fields: [challengeId], references: [id], onDelete: Cascade)
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([challengeId, userId])
  @@map("challenge_participants")
}

model PPVEvent {
  id          String        @id @default(cuid())
  title       String
  description String?
  price       Int
  scheduledAt DateTime
  duration    Int // in minutes
  thumbnail   String?
  status      String        @default("scheduled") // scheduled, live, completed, cancelled
  creatorId   String
  creator     User          @relation("PPVEvents", fields: [creatorId], references: [id])
  purchases   PPVPurchase[]
  startedAt   DateTime?
  endedAt     DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@map("ppv_events")
}

model PPVPurchase {
  id        String   @id @default(cuid())
  userId    String
  eventId   String
  amount    Int
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  event     PPVEvent @relation(fields: [eventId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@unique([userId, eventId])
  @@map("ppv_purchases")
}

model Tip {
  id          String   @id @default(cuid())
  fromUserId  String
  toUserId    String
  amount      Int
  platformFee Int      @default(0)
  message     String?
  fromUser    User     @relation("SentTips", fields: [fromUserId], references: [id], map: "sent_tips_from_user_fkey")
  toUser      User     @relation("ReceivedTips", fields: [toUserId], references: [id], map: "received_tips_to_user_fkey")
  createdAt   DateTime @default(now())

  @@map("tips")
}

model AffiliateProgram {
  id                  String                @id @default(cuid())
  name                String
  description         String?
  commissionRate      Float                 @default(0.1) // 10%
  creatorId           String
  creator             User                  @relation("AffiliatePrograms", fields: [creatorId], references: [id])
  affiliates          Affiliate[]
  links               AffiliateLink[]
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  AffiliateCommission AffiliateCommission[]

  @@map("affiliate_programs")
}

model Affiliate {
  id          String                @id @default(cuid())
  userId      String
  programId   String
  user        User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  program     AffiliateProgram      @relation(fields: [programId], references: [id], onDelete: Cascade)
  links       AffiliateLink[]
  commissions AffiliateCommission[]
  status      String                @default("active") // active, inactive, suspended
  createdAt   DateTime              @default(now())
  updatedAt   DateTime              @updatedAt

  @@unique([userId, programId])
  @@map("affiliates")
}

model AffiliateLink {
  id          String           @id @default(cuid())
  affiliateId String
  programId   String
  code        String           @unique
  url         String
  clicks      Int              @default(0)
  conversions Int              @default(0)
  affiliate   Affiliate        @relation(fields: [affiliateId], references: [id], onDelete: Cascade)
  program     AffiliateProgram @relation(fields: [programId], references: [id], onDelete: Cascade)
  createdAt   DateTime         @default(now())

  @@map("affiliate_links")
}

model AffiliateCommission {
  id          String           @id @default(cuid())
  affiliateId String
  programId   String
  userId      String? // User who made the purchase
  amount      Int // Commission amount
  status      String           @default("pending") // pending, paid, rejected
  affiliate   Affiliate        @relation(fields: [affiliateId], references: [id], onDelete: Cascade)
  program     AffiliateProgram @relation(fields: [programId], references: [id])
  user        User?            @relation("AffiliateCommissions", fields: [userId], references: [id])
  createdAt   DateTime         @default(now())
  paidAt      DateTime?

  @@map("affiliate_commissions")
}

model SupportTicket {
  id          String            @id @default(cuid())
  userId      String
  subject     String
  category    String
  priority    String            @default("medium")
  description String
  status      String            @default("open") // open, in-progress, resolved, closed
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  responses   SupportResponse[]
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  @@map("support_tickets")
}

model SupportResponse {
  id        String        @id @default(cuid())
  ticketId  String
  userId    String?
  message   String
  isAdmin   Boolean       @default(false)
  ticket    SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  user      User?         @relation("SupportResponses", fields: [userId], references: [id], map: "support_responses_user_fkey")
  createdAt DateTime      @default(now())

  @@map("support_responses")
}
