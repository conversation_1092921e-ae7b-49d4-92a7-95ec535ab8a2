import type { MetadataRoute } from "next";

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: "PremiumTube",
    short_name: "PremiumTube",
    description: "Premium video content platform",
    start_url: "/",
    display: "standalone",
    background_color: "#ffffff",
    theme_color: "#000000",
    icons: [
      {
        src: "/icons/icon-192x192.png",
        sizes: "192x192",
        type: "image/png",
      },
      {
        src: "/icons/icon-512x512.png",
        sizes: "512x512",
        type: "image/png",
      },
    ],
  };
}

export const metadata = {
  manifest: "/manifest.json",
};

export const dynamic = "force-dynamic";

export const revalidate = 3600; // 1 hour

export const priority = true;

export const dynamicParams = true;
