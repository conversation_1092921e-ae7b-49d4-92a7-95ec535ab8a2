import { <PERSON>ache<PERSON>anager } from "@/lib/cache-manager";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";

export class UserService {
  static async getUserProfile(userId: string) {
    const cacheKey = `user:profile:${userId}`;

    // Try cache first
    const cached = await CacheManager.get(cacheKey);
    if (cached) return cached;

    // Fetch from database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        balance: true,
        role: true,
        createdAt: true,
        _count: {
          select: {
            videos: true,
            subscriptions: true,
            purchases: true,
          },
        },
      },
    });

    if (user) {
      // Cache for 5 minutes
      await CacheManager.set(cacheKey, user, 300);
    }

    return user;
  }

  static async updateUserBalance(
    userId: string,
    amount: number,
    transactionType: "add" | "deduct"
  ) {
    try {
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          balance:
            transactionType === "add"
              ? { increment: amount }
              : { decrement: amount },
        },
        select: {
          id: true,
          balance: true,
        },
      });

      // Invalidate user cache
      await CacheManager.invalidateUserCache(userId);

      // Log transaction
      await this.logTransaction(userId, amount, transactionType);

      return updatedUser;
    } catch (error) {
      logger.error("Failed to update user balance:", error);
      throw error;
    }
  }

  private static async logTransaction(
    userId: string,
    amount: number,
    type: string
  ) {
    await prisma.$executeRaw`
      INSERT INTO transactions (user_id, amount, type, created_at)
      VALUES (${userId}, ${amount}, ${type}, NOW())
    `;
  }
}
