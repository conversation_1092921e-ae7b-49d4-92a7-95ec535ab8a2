import { prisma } from "@/lib/prisma";

export class PPVService {
  static async createEvent(data: {
    title: string;
    description: string;
    price: number;
    scheduledAt: Date;
    duration: number; // in minutes
    creatorId: string;
    thumbnail?: string;
  }) {
    // Validate price
    if (data.price < 5000) {
      throw new Error("Minimum PPV event price is Rp5,000");
    }

    return await prisma.ppvEvent.create({
      data: {
        ...data,
        status: "scheduled",
      },
    });
  }

  static async purchaseAccess(userId: string, eventId: string) {
    const event = await prisma.ppvEvent.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      throw new Error("Event not found");
    }

    if (event.status !== "scheduled") {
      throw new Error("Event is not available for purchase");
    }

    // Check if user already purchased
    const existingPurchase = await prisma.ppvPurchase.findUnique({
      where: {
        userId_eventId: {
          userId,
          eventId,
        },
      },
    });

    if (existingPurchase) {
      throw new Error("You already purchased access to this event");
    }

    // Check user balance
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.balance < event.price) {
      throw new Error("Insufficient balance");
    }

    // Process purchase
    const result = await prisma.$transaction(async (tx: any) => {
      // Create purchase record
      const purchase = await tx.ppvPurchase.create({
        data: {
          userId,
          eventId,
          amount: event.price,
        },
      });

      // Deduct user balance
      await tx.user.update({
        where: { id: userId },
        data: {
          balance: { decrement: event.price },
        },
      });

      // Add to creator balance (80%)
      const creatorAmount = Math.floor(event.price * 0.8);
      await tx.user.update({
        where: { id: event.creatorId },
        data: {
          balance: { increment: creatorAmount },
        },
      });

      // Add platform fee (20%)
      const platformFee = event.price - creatorAmount;
      await tx.user.update({
        where: { id: "platform" },
        data: {
          balance: { increment: platformFee },
        },
      });

      return purchase;
    });

    return result;
  }

  static async getUpcomingEvents(limit: number = 10) {
    return await prisma.ppvEvent.findMany({
      where: {
        status: "scheduled",
        scheduledAt: {
          gte: new Date(),
        },
      },
      include: {
        creator: {
          select: {
            name: true,
            image: true,
          },
        },
        _count: {
          select: {
            purchases: true,
          },
        },
      },
      orderBy: {
        scheduledAt: "asc",
      },
      take: limit,
    });
  }

  static async getUserEvents(userId: string) {
    return await prisma.ppvPurchase.findMany({
      where: {
        userId,
      },
      include: {
        event: {
          include: {
            creator: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  static async startEvent(eventId: string) {
    const event = await prisma.ppvEvent.findUnique({
      where: { id: eventId },
    });

    if (!event) {
      throw new Error("Event not found");
    }

    if (event.status !== "scheduled") {
      throw new Error("Event cannot be started");
    }

    // Check if scheduled time has passed
    if (event.scheduledAt > new Date()) {
      throw new Error("Event is not scheduled to start yet");
    }

    return await prisma.ppvEvent.update({
      where: { id: eventId },
      data: {
        status: "live",
        startedAt: new Date(),
      },
    });
  }

  static async endEvent(eventId: string) {
    return await prisma.ppvEvent.update({
      where: { id: eventId },
      data: {
        status: "completed",
        endedAt: new Date(),
      },
    });
  }
}
