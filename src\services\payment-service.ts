import { <PERSON><PERSON><PERSON>anager } from "@/lib/cache-manager";
import { AppError } from "@/lib/errors";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { stripe } from "@/lib/stripe";
import { UserService } from "@/services/user-service";

export class PaymentService {
  static async processTopUp(userId: string, amount: number) {
    try {
      // Create Stripe payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount,
        currency: "idr",
        metadata: {
          userId,
          type: "topup",
        },
      });

      return {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
      };
    } catch (error) {
      logger.error("Failed to create payment intent:", error);
      throw new AppError("Failed to process payment");
    }
  }

  static async handlePaymentSuccess(paymentIntentId: string) {
    try {
      const paymentIntent = await stripe.paymentIntents.retrieve(
        paymentIntentId
      );

      if (paymentIntent.status === "succeeded") {
        const userId = paymentIntent.metadata.userId;
        const amount = paymentIntent.amount;

        // Update user balance
        await UserService.updateUserBalance(userId, amount, "add");

        // Create transaction record
        await prisma.transaction.create({
          data: {
            userId,
            amount,
            type: "topup",
            status: "completed",
            paymentIntentId,
          },
        });

        return { success: true };
      }
    } catch (error) {
      logger.error("Failed to handle payment success:", error);
      throw error;
    }
  }

  static async processVideoPurchase(userId: string, videoId: string) {
    const video = await prisma.video.findUnique({
      where: { id: videoId },
    });

    if (!video) {
      throw new AppError("Video not found", 404);
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.balance < video.price) {
      throw new AppError("Insufficient balance", 400);
    }

    // Start database transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create purchase record
      const purchase = await tx.purchase.create({
        data: {
          userId,
          videoId,
          amount: video.price,
        },
      });

      // Deduct balance
      await tx.user.update({
        where: { id: userId },
        data: {
          balance: {
            decrement: video.price,
          },
        },
      });

      // Add to creator's earnings
      await tx.user.update({
        where: { id: video.creatorId },
        data: {
          balance: {
            increment: Math.floor(video.price * 0.8), // 80% to creator
          },
        },
      });

      return purchase;
    });

    // Invalidate caches
    await Promise.all([
      CacheManager.invalidateUserCache(userId),
      CacheManager.invalidateUserCache(video.creatorId),
      CacheManager.invalidateVideoCache(videoId),
    ]);

    return result;
  }
}
